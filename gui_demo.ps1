# PowerShell GUI 演示脚本
# 功能：输入框 -> 按钮点击 -> 编辑框输出 + 消息框弹出

Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# 创建主窗体
$form = New-Object System.Windows.Forms.Form
$form.Text = "PowerShell GUI 演示"
$form.Size = New-Object System.Drawing.Size(500, 400)
$form.StartPosition = "CenterScreen"
$form.FormBorderStyle = "FixedDialog"
$form.MaximizeBox = $false
$form.BackColor = [System.Drawing.Color]::LightBlue

# 创建标题标签
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Location = New-Object System.Drawing.Point(20, 20)
$titleLabel.Size = New-Object System.Drawing.Size(450, 30)
$titleLabel.Text = "PowerShell GUI 交互演示"
$titleLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 14, [System.Drawing.FontStyle]::Bold)
$titleLabel.ForeColor = [System.Drawing.Color]::DarkBlue
$titleLabel.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
$form.Controls.Add($titleLabel)

# 创建输入提示标签
$inputLabel = New-Object System.Windows.Forms.Label
$inputLabel.Location = New-Object System.Drawing.Point(20, 70)
$inputLabel.Size = New-Object System.Drawing.Size(100, 25)
$inputLabel.Text = "请输入内容："
$inputLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10)
$form.Controls.Add($inputLabel)

# 创建输入框
$inputTextBox = New-Object System.Windows.Forms.TextBox
$inputTextBox.Location = New-Object System.Drawing.Point(130, 68)
$inputTextBox.Size = New-Object System.Drawing.Size(250, 25)
$inputTextBox.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10)
$inputTextBox.BackColor = [System.Drawing.Color]::White
$inputTextBox.BorderStyle = "FixedSingle"
$form.Controls.Add($inputTextBox)

# 创建处理按钮
$processButton = New-Object System.Windows.Forms.Button
$processButton.Location = New-Object System.Drawing.Point(390, 65)
$processButton.Size = New-Object System.Drawing.Size(80, 30)
$processButton.Text = "处理"
$processButton.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10, [System.Drawing.FontStyle]::Bold)
$processButton.BackColor = [System.Drawing.Color]::LightGreen
$processButton.ForeColor = [System.Drawing.Color]::DarkGreen
$processButton.FlatStyle = "Flat"
$processButton.Cursor = "Hand"
$form.Controls.Add($processButton)

# 创建输出提示标签
$outputLabel = New-Object System.Windows.Forms.Label
$outputLabel.Location = New-Object System.Drawing.Point(20, 120)
$outputLabel.Size = New-Object System.Drawing.Size(100, 25)
$outputLabel.Text = "输出结果："
$outputLabel.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10)
$form.Controls.Add($outputLabel)

# 创建输出编辑框（多行文本框）
$outputTextBox = New-Object System.Windows.Forms.TextBox
$outputTextBox.Location = New-Object System.Drawing.Point(20, 150)
$outputTextBox.Size = New-Object System.Drawing.Size(450, 150)
$outputTextBox.Multiline = $true
$outputTextBox.ScrollBars = "Vertical"
$outputTextBox.ReadOnly = $true
$outputTextBox.Font = New-Object System.Drawing.Font("Consolas", 10)
$outputTextBox.BackColor = [System.Drawing.Color]::LightGray
$outputTextBox.BorderStyle = "Fixed3D"
$form.Controls.Add($outputTextBox)

# 创建清空按钮
$clearButton = New-Object System.Windows.Forms.Button
$clearButton.Location = New-Object System.Drawing.Point(20, 320)
$clearButton.Size = New-Object System.Drawing.Size(80, 30)
$clearButton.Text = "清空"
$clearButton.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10)
$clearButton.BackColor = [System.Drawing.Color]::LightCoral
$clearButton.ForeColor = [System.Drawing.Color]::DarkRed
$clearButton.FlatStyle = "Flat"
$clearButton.Cursor = "Hand"
$form.Controls.Add($clearButton)

# 创建退出按钮
$exitButton = New-Object System.Windows.Forms.Button
$exitButton.Location = New-Object System.Drawing.Point(390, 320)
$exitButton.Size = New-Object System.Drawing.Size(80, 30)
$exitButton.Text = "退出"
$exitButton.Font = New-Object System.Drawing.Font("Microsoft YaHei", 10)
$exitButton.BackColor = [System.Drawing.Color]::LightSalmon
$exitButton.ForeColor = [System.Drawing.Color]::DarkRed
$exitButton.FlatStyle = "Flat"
$exitButton.Cursor = "Hand"
$form.Controls.Add($exitButton)

# 处理按钮点击事件
$processButton.Add_Click({
    $inputText = $inputTextBox.Text.Trim()
    
    if ([string]::IsNullOrEmpty($inputText)) {
        [System.Windows.Forms.MessageBox]::Show(
            "请先输入一些内容！", 
            "提示", 
            [System.Windows.Forms.MessageBoxButtons]::OK, 
            [System.Windows.Forms.MessageBoxIcon]::Warning
        )
        $inputTextBox.Focus()
        return
    }
    
    # 获取当前时间
    $currentTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    # 在输出框中显示处理结果
    $outputText = @"
=== 处理时间：$currentTime ===
输入内容：$inputText
内容长度：$($inputText.Length) 个字符
处理状态：成功
反转内容：$(-join $inputText.ToCharArray()[($inputText.Length-1)..0])
大写转换：$($inputText.ToUpper())
小写转换：$($inputText.ToLower())
================================

"@
    
    # 追加到输出框
    $outputTextBox.Text += $outputText
    $outputTextBox.SelectionStart = $outputTextBox.Text.Length
    $outputTextBox.ScrollToCaret()
    
    # 弹出消息框
    $result = [System.Windows.Forms.MessageBox]::Show(
        "处理完成！`n`n输入内容：$inputText`n处理时间：$currentTime`n`n是否清空输入框？", 
        "处理结果", 
        [System.Windows.Forms.MessageBoxButtons]::YesNo, 
        [System.Windows.Forms.MessageBoxIcon]::Information
    )
    
    # 根据用户选择决定是否清空输入框
    if ($result -eq [System.Windows.Forms.DialogResult]::Yes) {
        $inputTextBox.Clear()
        $inputTextBox.Focus()
    }
})

# 清空按钮点击事件
$clearButton.Add_Click({
    $result = [System.Windows.Forms.MessageBox]::Show(
        "确定要清空所有内容吗？", 
        "确认清空", 
        [System.Windows.Forms.MessageBoxButtons]::YesNo, 
        [System.Windows.Forms.MessageBoxIcon]::Question
    )
    
    if ($result -eq [System.Windows.Forms.DialogResult]::Yes) {
        $inputTextBox.Clear()
        $outputTextBox.Clear()
        $inputTextBox.Focus()
        [System.Windows.Forms.MessageBox]::Show(
            "内容已清空！", 
            "操作完成", 
            [System.Windows.Forms.MessageBoxButtons]::OK, 
            [System.Windows.Forms.MessageBoxIcon]::Information
        )
    }
})

# 退出按钮点击事件
$exitButton.Add_Click({
    $result = [System.Windows.Forms.MessageBox]::Show(
        "确定要退出程序吗？", 
        "确认退出", 
        [System.Windows.Forms.MessageBoxButtons]::YesNo, 
        [System.Windows.Forms.MessageBoxIcon]::Question
    )
    
    if ($result -eq [System.Windows.Forms.DialogResult]::Yes) {
        $form.Close()
    }
})

# 支持回车键触发处理
$inputTextBox.Add_KeyDown({
    if ($_.KeyCode -eq [System.Windows.Forms.Keys]::Enter) {
        $processButton.PerformClick()
    }
})

# 窗体关闭事件
$form.Add_FormClosing({
    $result = [System.Windows.Forms.MessageBox]::Show(
        "确定要关闭程序吗？", 
        "确认关闭", 
        [System.Windows.Forms.MessageBoxButtons]::YesNo, 
        [System.Windows.Forms.MessageBoxIcon]::Question
    )
    
    if ($result -eq [System.Windows.Forms.DialogResult]::No) {
        $_.Cancel = $true
    }
})

# 设置默认焦点
$inputTextBox.Focus()

# 显示窗体
Write-Host "正在启动 PowerShell GUI 演示程序..." -ForegroundColor Green
Write-Host "程序功能：" -ForegroundColor Yellow
Write-Host "1. 在输入框中输入内容" -ForegroundColor Cyan
Write-Host "2. 点击'处理'按钮或按回车键" -ForegroundColor Cyan
Write-Host "3. 查看输出结果和弹出的消息框" -ForegroundColor Cyan
Write-Host "4. 可以使用'清空'和'退出'按钮" -ForegroundColor Cyan
Write-Host ""

$form.ShowDialog() | Out-Null
